import json
import os
import re
from collections import Counter
from typing import Dict, List, <PERSON><PERSON>

def extract_answer_from_response(response: str) -> str:
    """
    从回答中提取最终答案
    支持多种格式：(A), (B), (C), (D) 或者数字答案
    """
    # 查找 (X) 格式的答案
    pattern_parentheses = r'\(([A-D])\)'
    matches = re.findall(pattern_parentheses, response)
    if matches:
        return matches[-1]  # 返回最后一个匹配的答案
    
    # 查找 boxed{} 格式的答案（用于数学题）
    pattern_boxed = r'\\boxed\{([^}]+)\}'
    matches = re.findall(pattern_boxed, response)
    if matches:
        return matches[-1].strip()
    
    # 查找 "answer is X" 格式
    pattern_answer_is = r'answer is ([A-D]|\d+)'
    matches = re.findall(pattern_answer_is, response, re.IGNORECASE)
    if matches:
        return matches[-1]
    
    # 查找 "Therefore, X" 格式
    pattern_therefore = r'Therefore,?\s*([A-D]|\d+)'
    matches = re.findall(pattern_therefore, response, re.IGNORECASE)
    if matches:
        return matches[-1]
    
    # 如果都没找到，返回空字符串
    return ""

def majority_vote(answers: List[str]) -> str:
    """
    对答案列表进行多数投票
    """
    if not answers:
        return ""
    
    # 过滤掉空答案
    valid_answers = [ans for ans in answers if ans.strip()]
    
    if not valid_answers:
        return ""
    
    # 计算每个答案的出现次数
    counter = Counter(valid_answers)
    
    # 返回出现次数最多的答案
    most_common = counter.most_common(1)
    return most_common[0][0] if most_common else ""

def process_single_file(file_path: str) -> Tuple[int, int, List[Dict]]:
    """
    处理单个JSON文件，返回正确数量、总数量和详细结果
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    correct_count = 0
    total_count = 0
    detailed_results = []
    
    for question, question_data in data.items():
        total_count += 1
        
        # 提取所有agent的回答
        agent_answers = []
        for agent_context in question_data.get('agent_contexts', []):
            response = agent_context.get('response', '')
            extracted_answer = extract_answer_from_response(response)
            if extracted_answer:
                agent_answers.append(extracted_answer)
        
        # 进行多数投票
        voted_answer = majority_vote(agent_answers)
        
        # 获取正确答案
        correct_answer = question_data.get('correct_answer', '')
        
        # 判断是否正确
        is_correct = voted_answer == correct_answer
        if is_correct:
            correct_count += 1
        
        # 记录详细结果
        detailed_results.append({
            'question_index': question_data.get('question_index', total_count - 1),
            'question': question[:100] + "..." if len(question) > 100 else question,
            'agent_answers': agent_answers,
            'voted_answer': voted_answer,
            'correct_answer': correct_answer,
            'is_correct': is_correct
        })
    
    return correct_count, total_count, detailed_results

def main():
    """
    主函数：处理所有数据集文件并生成准确率报告
    """
    answer_dir = "answer"
    results = {}
    
    # 处理根目录下的文件
    root_files = [
        "gpqa_final_198questions.json",
        "gsm8k_final_1319questions.json", 
        "openbookqa_final_500questions.json"
    ]
    
    for filename in root_files:
        file_path = os.path.join(answer_dir, filename)
        if os.path.exists(file_path):
            print(f"处理文件: {filename}")
            correct, total, details = process_single_file(file_path)
            accuracy = correct / total if total > 0 else 0
            
            dataset_name = filename.replace("_final_", "_").replace("questions.json", "")
            results[dataset_name] = {
                'correct': correct,
                'total': total,
                'accuracy': accuracy,
                'details': details
            }
            print(f"  准确率: {accuracy:.4f} ({correct}/{total})")
    
    # 处理MMLU子目录
    mmlu_dir = os.path.join(answer_dir, "mmlu_answer")
    if os.path.exists(mmlu_dir):
        mmlu_files = [f for f in os.listdir(mmlu_dir) if f.endswith('.json')]
        
        for filename in mmlu_files:
            file_path = os.path.join(mmlu_dir, filename)
            print(f"处理文件: mmlu_answer/{filename}")
            correct, total, details = process_single_file(file_path)
            accuracy = correct / total if total > 0 else 0
            
            dataset_name = filename.replace("_final_", "_").replace("questions.json", "")
            results[dataset_name] = {
                'correct': correct,
                'total': total,
                'accuracy': accuracy,
                'details': details
            }
            print(f"  准确率: {accuracy:.4f} ({correct}/{total})")
    
    # 生成总结报告
    generate_report(results)

def generate_report(results: Dict):
    """
    生成准确率报告文件
    """
    with open("majority_voting_accuracy_report.txt", "w", encoding="utf-8") as f:
        f.write("=" * 80 + "\n")
        f.write("Majority Voting 准确率报告\n")
        f.write("=" * 80 + "\n\n")
        
        # 按数据集分类显示结果
        f.write("各数据集准确率:\n")
        f.write("-" * 50 + "\n")
        
        total_correct = 0
        total_questions = 0
        
        for dataset_name, result in results.items():
            accuracy = result['accuracy']
            correct = result['correct']
            total = result['total']
            
            f.write(f"{dataset_name:<40} {accuracy:>8.4f} ({correct:>4}/{total:<4})\n")
            
            total_correct += correct
            total_questions += total
        
        # 总体准确率
        overall_accuracy = total_correct / total_questions if total_questions > 0 else 0
        f.write("-" * 50 + "\n")
        f.write(f"{'总体准确率':<40} {overall_accuracy:>8.4f} ({total_correct:>4}/{total_questions:<4})\n")
        f.write("=" * 80 + "\n\n")
        
        # 详细结果（可选，如果需要的话）
        f.write("详细结果:\n")
        f.write("-" * 80 + "\n")
        
        for dataset_name, result in results.items():
            f.write(f"\n{dataset_name}:\n")
            for detail in result['details'][:5]:  # 只显示前5个问题的详细信息
                f.write(f"  问题 {detail['question_index']}: {detail['question']}\n")
                f.write(f"    Agent答案: {detail['agent_answers']}\n")
                f.write(f"    投票结果: {detail['voted_answer']}\n")
                f.write(f"    正确答案: {detail['correct_answer']}\n")
                f.write(f"    是否正确: {'✓' if detail['is_correct'] else '✗'}\n\n")
    
    print(f"\n报告已生成: majority_voting_accuracy_report.txt")
    print(f"总体准确率: {overall_accuracy:.4f} ({total_correct}/{total_questions})")

if __name__ == "__main__":
    main()
